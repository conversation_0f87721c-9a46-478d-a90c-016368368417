const mongoose = require('mongoose');

const connectDB = async () => {
    try{
        await mongoose.connect(process.config.env.MONGO_URI,{
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log("MongoDB connected");
    }catch(err){
        console.err(`Database connection error: ${err.message}`);
        process.exit(1);
    }
}

module.exports = connectDB();